body {
  margin: 0;
  font-family: system-ui, sans-serif;
  background: #f9fafb;
  color: #0f172a;
}
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #0ea5e9;
  color: white;
}
h1 { margin: 0; font-size: 18px; }
.badge {
  background: #111827;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
}
.grid {
  display: grid;
  grid-template-columns: 1.4fr 1fr 1fr;
  gap: 14px;
  padding: 14px;
}
.card {
  background: white;
  padding: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0,0,0,.1);
}
.video-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}
video {
  width: 100%;
  aspect-ratio: 16 / 9;
  background: #111;
  border-radius: 8px;
}
.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 10px 0;
}
button {
  padding: 8px 10px;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
  cursor: pointer;
}
button.danger { background: #fca5a5; }
.scrollbox {
  height: 180px;
  overflow: auto;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  padding: 6px;
  background: #f1f5f9;
}
textarea {
  width: 100%;
  min-height: 90px;
  margin: 4px 0;
}
.list { padding-left: 18px; }
#engBars .group { margin-bottom: 8px; }
#engBars .bar {
  height: 10px;
  background: #e2e8f0;
  border-radius: 6px;
  position: relative;
}
#engBars .bar span {
  position: absolute;
  left: 0; top: 0; bottom: 0;
  background: #0ea5e9;
  border-radius: 6px;
}
.label { font-size: 12px; margin-top: 2px; }
footer {
  text-align: center;
  padding: 10px;
  color: #475569;
}
