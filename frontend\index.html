<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Adaptive AI Meeting Twin</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <header>
    <h1>Adaptive AI Meeting Twin</h1>
    <div id="netMode" class="badge">Mode: Normal</div>
    <div id="stats">RTT: — | Loss(v/a): — / — | Out: — kbps</div>
  </header>

  <main class="grid">
    <!-- Video & Controls -->
    <section class="card">
      <div class="video-grid">
        <video id="localVideo" autoplay playsinline muted></video>
        <video id="remoteVideo" autoplay playsinline></video>
      </div>

      <div class="controls">
        <button id="btnStart">Start Devices</button>
        <button id="btnCreate" disabled>Create Offer</button>
        <button id="btnAnswer" disabled>Create Answer</button>
        <button id="btnSetOffer">Set Remote Offer</button>
        <button id="btnSetAnswer">Set Remote Answer</button>
        <button id="btnLeave" class="danger">Leave</button>
      </div>

      <details class="sigbox">
        <summary>Manual Signaling</summary>
        <textarea id="offerSdp" placeholder="Local Offer/Answer"></textarea>
        <textarea id="answerSdp" placeholder="Paste remote SDP here"></textarea>
      </details>
    </section>

    <!-- Transcript / MOM -->
    <section class="card">
      <h2>Live Transcript</h2>
      <div id="transcript" class="scrollbox"></div>
      <label><input type="checkbox" id="chkTranscribe"> Enable mic transcription</label>

      <div class="controls">
        <button id="btnSummarize">Summarize → MOM & Actions</button>
      </div>

      <h3>MOM</h3>
      <ul id="mom" class="list"></ul>

      <h3>Action Items</h3>
      <ul id="actions" class="list"></ul>
    </section>

    <!-- Engagement / Sentiment / Recording -->
    <section class="card">
      <h2>Engagement</h2>
      <div id="engBars"></div>
      <div>
        <span id="champ" class="badge">🏆 Meeting Champ: —</span>
        <span id="silent" class="badge">🤫 Silent Listener: —</span>
      </div>

      <h2>Sentiment</h2>
      <canvas id="sentChart" width="360" height="120"></canvas>

      <h2>Recording</h2>
      <div class="controls">
        <button id="btnRecStart">Start Recording</button>
        <button id="btnRecStop">Stop Recording</button>
      </div>
      <video id="playback" controls></video>
    </section>
  </main>

  <footer>
    <small>
      Adaptive fallback: <b>Normal</b> → <b>Degraded video</b> → <b>Audio-only</b> → <b>Transcript-only</b>
    </small>
  </footer>

  <script src="app.js"></script>
</body>
</html>
