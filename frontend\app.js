// Connect to Flask-SocketIO backend
const socket = io("http://localhost:6000");

let pc, localStream, remoteStream;

async function start(room) {
  // 1. Get camera/mic
  localStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
  document.getElementById("localVideo").srcObject = localStream;

  // 2. Create PeerConnection
  pc = new RTCPeerConnection();

  // 3. Add local tracks
  localStream.getTracks().forEach(track => pc.addTrack(track, localStream));

  // 4. Remote stream
  remoteStream = new MediaStream();
  document.getElementById("remoteVideo").srcObject = remoteStream;
  pc.ontrack = event => {
    event.streams[0].getTracks().forEach(track => remoteStream.addTrack(track));
  };

  // 5. ICE candidates
  pc.onicecandidate = event => {
    if (event.candidate) {
      socket.emit("signal", { room, data: { candidate: event.candidate } });
    }
  };

  // 6. Join room
  socket.emit("join", { room });

  // 7. Listen for signaling messages
  socket.on("signal", async ({ data }) => {
    if (data.sdp) {
      // Got offer or answer
      await pc.setRemoteDescription(new RTCSessionDescription(data.sdp));
      if (data.sdp.type === "offer") {
        // If it's an offer → generate answer
        const answer = await pc.createAnswer();
        await pc.setLocalDescription(answer);
        socket.emit("signal", { room, data: { sdp: answer } });
      }
    } else if (data.candidate) {
      // Got ICE candidate
      try {
        await pc.addIceCandidate(new RTCIceCandidate(data.candidate));
      } catch (err) {
        console.error("Error adding ICE candidate:", err);
      }
    }
  });
}

// Function for the first peer to create an offer
async function createOffer(room) {
  const offer = await pc.createOffer();
  await pc.setLocalDescription(offer);
  socket.emit("signal", { room, data: { sdp: offer } });
}

// Example: Auto-start in "room1"
start("room1").then(() => {
  // First peer should call this
  createOffer("room1");
});
