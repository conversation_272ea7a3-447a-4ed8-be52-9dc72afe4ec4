from flask import Flask, request
from flask_socketio import <PERSON>cket<PERSON>, join_room, emit

app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins="*")

@app.route("/")
def home():
    return "Signaling server is running"

@socketio.on("join")
def on_join(data):
    room = data.get("room")
    join_room(room)
    emit("peer-joined", {"id": request.sid}, room=room, include_self=False)

@socketio.on("signal")
def on_signal(data):
    room = data.get("room")
    signal_data = data.get("data")
    emit("signal", {"id": request.sid, "data": signal_data}, room=room, include_self=False)

@socketio.on("disconnect")
def on_disconnect():
    print("User disconnected", request.sid)

if __name__ == "__main__":
    # force Werkzeug on Windows, change port if needed
    socketio.run(app, host="0.0.0.0", port=6000, debug=True, allow_unsafe_werkzeug=True)
